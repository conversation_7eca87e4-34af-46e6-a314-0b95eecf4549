{% extends "base.html" %}
{% load static %}
{% load avatar_tags %}
{% load json_filters %}
{% load item_tags %}
{% load cache %}
{% load humanize %}

{% block css %}
    <link href="{{MEDIA_URL}}css/view-peak.css?v=1.2" rel="stylesheet" type="text/css" media="screen" />
    <link href="{% static 'css/view-peak-optimized.css' %}" rel="stylesheet" type="text/css" media="screen" />
{% endblock %}

{% block title %}{{ peak_data.peakname_title }} - {{ peak_data.peaklocation_title }}{% endblock %}
{% block titlemeta %}{{ peak_data.peakname_title }} - {{ peak_data.peaklocation_title }}{% endblock %}
{% block description %}{{ peak_data.peak_meta_description }}{% endblock %}
{% block image_rel %}{{ peak_data.thumbnail_910 }}{% endblock %}

{% block meta_facebook %}
    <meta property="og:title" content="{{ peak.name }}"/>
    <meta property="og:site_name" content="peakery.com"/>
    <meta property="og:type" content="website"/>
    <meta property="og:url" content="{{ peak_data.absolute_url }}"/>
    <meta property="og:image" content="{{ peak_data.thumbnail_910 }}"/>
    <meta property="og:description" content="{{ peak_data.peak_meta_description }}"/>
{% endblock %}

{% block meta_google %}
<meta itemprop="name" content="{{ peak.name }}">
<meta itemprop="description" content="{{ peak_data.peak_meta_description }}">
<meta itemprop="image" content="{{ peak_data.thumbnail_910 }}">
{% endblock %}

{% block meta_twitter %}
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="{{ peak.name }}">
<meta name="twitter:description" content="{{ peak_data.peak_meta_description }}">
<meta name="twitter:image" content="{{ peak_data.thumbnail_910 }}">
{% endblock %}

{% block js_globals %}
    var peak_id = {{ peak.id }};
    var peakObject = {{ peak_data|jsonify|safe }};
{% endblock %}

{% block extrajs %}
    <script src="{% static 'js/jquery.cycle.lite.js' %}"></script>
    <script type="text/javascript">
    {% include "mapbox/polyline.js" %}
    </script>
    <script src="{% static 'js/GPXParser.js'%}"></script>
    <script src="{% static 'js/view-peak-optimized.js' %}"></script>
{% endblock %}

{% block mapbox %}
{% include "mapbox/mapbox.html" %}
{% endblock %}

{% block fixed_page_header %}
    {% include "items/partials/peak_header.html" %}
{% endblock %}

{% block content %}
    <script src="{% static 'vendor/s3.fine-uploader/s3.fine-uploader.js' %}"></script>
    
    {% include "items/partials/peak_schema.html" %}

    <div class="container">
        {% include "items/partials/mobile_header.html" %}
        
        <div class="row content-pane peak-content-pane">
            {% include "items/partials/peak_photo_section.html" %}
            {% include "items/partials/peak_map_section.html" %}
        </div>

        {% include "items/partials/peak_stats_grid.html" %}
        
        {% if user.is_authenticated %}
            {% include "items/partials/edit_peak_info_mobile.html" %}
        {% endif %}
        
        {% if user.is_superuser %}
            {% include "items/partials/admin_challenges_section.html" %}
        {% endif %}

        <div class="row dark-background-row">
            <div class="sp-60"></div>
        </div>

        {% include "items/partials/highlights_section.html" %}

        <div class="row dark-background-row">
            <div class="sp-60"></div>
        </div>

        {% if routes %}
            {% include "items/partials/routes_section.html" %}
            <div class="row dark-background-row">
                <div class="sp-60"></div>
            </div>
        {% endif %}

        {% if featured_logs %}
            {% include "items/partials/featured_logs_section.html" %}
            <div class="row dark-background-row">
                <div class="sp-60"></div>
            </div>
        {% endif %}

        {% include "items/partials/photos_section.html" %}

        <div class="row dark-background-row">
            <div class="sp-60"></div>
        </div>

        {% if summits_count > 0 %}
            {% include "items/partials/awards_section.html" %}
            <div class="row dark-background-row">
                <div class="sp-60"></div>
            </div>
        {% endif %}

        {% if challenges %}
            {% include "items/partials/challenges_section.html" %}
            <div class="row dark-background-row">
                <div class="sp-60"></div>
            </div>
        {% endif %}

        {% include "items/partials/peak_seo_cards.html" %}

        <div class="row hidden-lg hidden-md hidden-sm">
            <div style="height: 180px;"></div>
        </div>
        <div class="row hidden-xs">
            <div style="height: 56px;"></div>
        </div>

        {% include "items/partials/modals.html" %}
        
        {% if request.user.is_superuser %}
            {% include "items/partials/admin_modals.html" %}
        {% endif %}
    </div>

    <script type="text/javascript">
        // Initialize peak view functionality
        $(document).ready(function() {
            PeakView.init({
                peak_id: {{ peak.id }},
                peak_slug: '{{ peak.slug_new_text }}',
                peak_name: '{{ peak.name }}',
                peak_lat: {{ peak.lat }},
                peak_lng: {{ peak.long }},
                is_authenticated: {% if user.is_authenticated %}true{% else %}false{% endif %},
                is_staff: {% if user.is_staff %}true{% else %}false{% endif %},
                is_superuser: {% if user.is_superuser %}true{% else %}false{% endif %},
                photos_count: {{ peak_photos_count|default:0 }},
                initial_highlights: [
                    {% for h in highlights %}'{{ h.highlight|escapejs }}'{% if not forloop.last %},{% endif %}{% endfor %}
                ]
            });
        });
    </script>

{% endblock %}
